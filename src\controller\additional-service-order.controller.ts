import {
  Controller,
  Get,
  Post,
  Del,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import {
  AdditionalServiceOrderService,
  CreateAdditionalServiceOrderData,
} from '../service/additional-service-order.service';
import { CustomError } from '../error/custom.error';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderStatus,
} from '../entity/additional-service-order.entity';
import { AdditionalServiceOrderDetail } from '../entity/additional-service-order-detail.entity';
import { AdditionalServiceDiscountInfo } from '../entity/additional-service-discount-info.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Order } from '../entity/order.entity';
import { Customer } from '../entity/customer.entity';
import { Employee } from '../entity/employee.entity';
import { Service } from '../entity/service.entity';
import { DiscountType } from '../entity/order-discount-info.entity';

@Controller('/order-details/:orderDetailId/additional-services')
export class AdditionalServiceOrderController {
  @Inject()
  service: AdditionalServiceOrderService;

  @Post('/', { summary: '创建追加服务申请' })
  async create(
    @Param('orderDetailId') orderDetailId: number,
    @Body()
    body: {
      customerId: number;
      services: {
        serviceId: number;
        quantity: number;
      }[];
      discountInfos?: {
        discountType: DiscountType;
        discountId: number;
        discountAmount: number;
      }[];
      remark?: string;
    }
  ) {
    const { customerId, services, discountInfos, remark } = body;

    if (!customerId) {
      throw new CustomError('客户ID不能为空');
    }

    if (!services || services.length === 0) {
      throw new CustomError('追加服务不能为空');
    }

    // 验证服务数据
    for (const serviceItem of services) {
      if (!serviceItem.serviceId) {
        throw new CustomError('服务ID不能为空');
      }
      if (!serviceItem.quantity || serviceItem.quantity <= 0) {
        throw new CustomError('服务数量必须大于0');
      }
    }

    const createData: CreateAdditionalServiceOrderData = {
      orderDetailId,
      customerId,
      services,
      discountInfos,
      remark,
    };

    return await this.service.createAdditionalServiceOrder(createData);
  }

  @Get('/', { summary: '查询追加服务列表' })
  async list(
    @Param('orderDetailId') orderDetailId: number,
    @Query('status') status?: string
  ) {
    const where: any = { orderDetailId };

    if (status) {
      where.status = status;
    }

    return await AdditionalServiceOrder.findAll({
      where,
      include: [
        {
          model: AdditionalServiceOrderDetail,
          include: [Service],
        },
        {
          model: AdditionalServiceDiscountInfo,
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  @Get('/:id', { summary: '查询追加服务详情' })
  async show(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
      include: [
        {
          model: AdditionalServiceOrderDetail,
          include: [Service],
        },
        {
          model: AdditionalServiceDiscountInfo,
        },
        {
          model: OrderDetail,
          include: [
            {
              model: Order,
              include: [Customer, Employee],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return additionalServiceOrder;
  }

  @Post('/:id/confirm', { summary: '员工确认追加服务' })
  async confirm(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.confirmAdditionalService(id, employeeId);
  }

  @Post('/:id/reject', { summary: '员工拒绝追加服务' })
  async reject(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body()
    body: {
      employeeId: number;
      rejectReason: string;
    }
  ) {
    const { employeeId, rejectReason } = body;

    if (!employeeId) {
      throw new CustomError('员工ID不能为空');
    }

    if (!rejectReason) {
      throw new CustomError('拒绝原因不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.rejectAdditionalService(
      id,
      employeeId,
      rejectReason
    );
  }

  @Post('/:id/pay', { summary: '支付追加服务订单' })
  async pay(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('客户ID不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.payAdditionalServiceOrder(id, customerId);
  }

  @Del('/:id', { summary: '删除追加服务申请' })
  async delete(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('客户ID不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.deleteAdditionalServiceOrder(id, customerId);
  }

  @Get('/:id/status', { summary: '查询追加服务订单状态' })
  async getStatus(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number
  ) {
    const order = await AdditionalServiceOrder.findOne({
      where: { id, orderDetailId },
      attributes: ['id', 'sn', 'status', 'totalFee', 'confirmTime', 'payTime', 'createdAt', 'updatedAt']
    });

    console.log('【查询追加服务订单状态】：', order);

    return order;
  }
}

// 员工端查询待确认的追加服务列表
@Controller('/employee/additional-services')
export class EmployeeAdditionalServiceController {
  @Get('/pending', { summary: '查询待确认的追加服务列表' })
  async getPendingList(
    @Query('employeeId') employeeId: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空');
    }

    // 确保分页参数是数字类型
    const currentNum = Number(current);
    const pageSizeNum = Number(pageSize);
    const offset = (currentNum - 1) * pageSizeNum;

    const result = await AdditionalServiceOrder.findAndCountAll({
      where: {
        employeeId,
        status: AdditionalServiceOrderStatus.PENDING_CONFIRM,
      },
      include: [
        {
          model: AdditionalServiceOrderDetail,
          include: [Service],
        },
        {
          model: OrderDetail,
          include: [
            {
              model: Order,
              include: [Customer],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: pageSizeNum,
    });

    return {
      list: result.rows,
      total: result.count,
      current: currentNum,
      pageSize: pageSizeNum,
    };
  }
}
