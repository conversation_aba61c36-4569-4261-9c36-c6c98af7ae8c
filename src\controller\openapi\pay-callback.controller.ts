import {
  Controller,
  Inject,
  Post,
  Body,
  Logger,
  ILogger,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Order } from '../../entity/order.entity';
import { OrderStatus, OrderStatusChangeType } from '../../common/Constant';
import { ServiceChangeLog } from '../../entity/service-change-log.entity';
import {
  MembershipCardOrder,
  MembershipCardOrderStatus,
} from '../../entity/membership-card-order.entity';
import { CustomerMembershipCardService } from '../../service/customer-membership-card.service';
import { MembershipCardType } from '../../entity/membership-card-type.entity';
import {
  CouponOrder,
  CouponOrderStatus,
} from '../../entity/coupon-order.entity';
import { CustomerCouponService } from '../../service/customer-coupon.service';
import { Coupon } from '../../entity/coupon.entity';
import { CustomerService } from '../../service/customer.service';
import { MessageBroadcastService } from '../../service/message-broadcast.service';
import { AdditionalServiceOrder, AdditionalServiceOrderStatus } from '../../entity/additional-service-order.entity';
import { AdditionalServiceOrderService } from '../../service/additional-service-order.service';

@Controller('/openapi/pay')
export class PayCallbackController {
  @Inject()
  ctx: Context;

  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Inject()
  customerCouponService: CustomerCouponService;

  @Inject()
  customerService: CustomerService;

  @Inject()
  messageBroadcastService: MessageBroadcastService;

  @Inject()
  additionalServiceOrderService: AdditionalServiceOrderService;

  @Logger()
  logger: ILogger;

  @Post('/callback', { summary: '微信支付回调' })
  async callback(@Body() body: any) {
    this.logger.info('【微信支付回调】：', body);

    try {
      // 解析回调数据
      const { resource } = body;
      const { ciphertext, nonce, associated_data } = resource;

      console.log(
        'ciphertext, nonce, associated_data:',
        ciphertext,
        nonce,
        associated_data
      );

      // TODO: 解密数据，这里需要使用微信支付API v3密钥解密
      // 由于解密需要使用微信支付API v3密钥，这里简化处理，直接假设解密后的数据
      // 实际项目中需要实现正确的解密逻辑
      const decryptedData = {
        out_trade_no: '', // 这里应该是解密后的商户订单号
        transaction_id: '', // 这里应该是解密后的微信支付订单号
        trade_state: 'SUCCESS', // 这里应该是解密后的交易状态
      };

      // 获取商户订单号
      const { out_trade_no, transaction_id, trade_state } = decryptedData;

      // 判断是服务订单、权益卡订单、代金券订单还是追加服务订单
      if (out_trade_no.startsWith('MC')) {
        // 权益卡订单
        await this.handleMembershipCardOrderCallback(
          out_trade_no,
          transaction_id,
          trade_state
        );
      } else if (out_trade_no.startsWith('CP')) {
        // 代金券订单
        await this.handleCouponOrderCallback(
          out_trade_no,
          transaction_id,
          trade_state
        );
      } else if (out_trade_no.startsWith('ADD')) {
        // 追加服务订单
        await this.handleAdditionalServiceOrderCallback(
          out_trade_no,
          transaction_id,
          trade_state
        );
      } else {
        // 服务订单
        await this.handleServiceOrderCallback(
          out_trade_no,
          transaction_id,
          trade_state
        );
      }

      // 返回成功
      return {
        code: 'SUCCESS',
        message: '成功',
      };
    } catch (error) {
      this.logger.error('【微信支付回调处理错误】：', error);
      // 返回失败
      return {
        code: 'FAIL',
        message: '失败',
      };
    }
  }

  /**
   * 处理服务订单支付回调
   * @param out_trade_no 商户订单号
   * @param transaction_id 微信支付订单号
   * @param trade_state 交易状态
   */
  private async handleServiceOrderCallback(
    out_trade_no: string,
    transaction_id: string,
    trade_state: string
  ) {
    // 查询订单
    const order = await Order.findOne({
      where: {
        sn: out_trade_no,
      },
    });

    if (!order) {
      this.logger.error('【微信支付回调】订单不存在：', out_trade_no);
      return;
    }

    // 判断交易状态
    if (trade_state === 'SUCCESS') {
      if (order.status === OrderStatus.待付款) {
        // 更新订单状态
        await order.update({
          status: OrderStatus.待接单,
          sn: transaction_id,
        });

        // 广播新订单消息
        await this.messageBroadcastService.broadcastNewOrder(order.id);
      }

      // 记录变更日志
      await ServiceChangeLog.create({
        orderId: order.id,
        changeType: OrderStatusChangeType.付款,
        description: '微信支付回调：支付成功',
      });

      this.logger.info('【微信支付回调】订单支付成功：', out_trade_no);
    } else {
      this.logger.info('【微信支付回调】订单支付状态异常：', trade_state);
    }
  }

  /**
   * 处理权益卡订单支付回调
   * @param out_trade_no 商户订单号
   * @param transaction_id 微信支付订单号
   * @param trade_state 交易状态
   */
  private async handleMembershipCardOrderCallback(
    out_trade_no: string,
    transaction_id: string,
    trade_state: string
  ) {
    // 查询订单
    const order = await MembershipCardOrder.findOne({
      where: {
        sn: out_trade_no,
      },
    });

    if (!order) {
      this.logger.error('【微信支付回调】权益卡订单不存在：', out_trade_no);
      return;
    }

    // 判断交易状态
    if (trade_state === 'SUCCESS') {
      // 更新订单状态
      await order.update({
        status: MembershipCardOrderStatus.PAID,
        payTime: new Date(),
      });

      // 查询权益卡类型信息
      const cardType = await MembershipCardType.findByPk(order.cardTypeId);
      if (!cardType) {
        this.logger.error(
          '【微信支付回调】权益卡类型不存在：',
          order.cardTypeId
        );
        return;
      }

      // 创建用户权益卡
      const now = new Date();
      let expiryTime = null;
      if (cardType.validDays) {
        expiryTime = new Date(
          now.getTime() + cardType.validDays * 24 * 60 * 60 * 1000
        );
      }

      // 设置剩余次数
      let remainTimes = -1; // 默认不限次数
      if (
        cardType.type === 'times' &&
        cardType.usageLimit !== null &&
        cardType.usageLimit !== undefined
      ) {
        remainTimes = cardType.usageLimit;
      }

      // 创建用户权益卡
      await this.customerMembershipCardService.createCard({
        customerId: order.customerId,
        cardTypeId: order.cardTypeId,
        purchaseTime: now,
        expiryTime,
        remainTimes,
        status: 'active',
      });

      // 更新用户会员状态为权益会员
      await this.customerService.updateMemberStatus(order.customerId, 1);

      this.logger.info('【微信支付回调】权益卡订单支付成功：', out_trade_no);
    } else {
      this.logger.info('【微信支付回调】权益卡订单支付状态异常：', trade_state);
    }
  }

  /**
   * 处理代金券订单支付回调
   * @param out_trade_no 商户订单号
   * @param transaction_id 微信支付订单号
   * @param trade_state 交易状态
   */
  private async handleCouponOrderCallback(
    out_trade_no: string,
    transaction_id: string,
    trade_state: string
  ) {
    // 查询订单
    const order = await CouponOrder.findOne({
      where: {
        sn: out_trade_no,
      },
    });

    if (!order) {
      this.logger.error('【微信支付回调】代金券订单不存在：', out_trade_no);
      return;
    }

    // 判断交易状态
    if (trade_state === 'SUCCESS') {
      // 更新订单状态
      await order.update({
        status: CouponOrderStatus.PAID,
        payTime: new Date(),
      });

      // 查询代金券信息
      const coupon = await Coupon.findByPk(order.couponId);
      if (!coupon) {
        this.logger.error('【微信支付回调】代金券不存在：', order.couponId);
        return;
      }

      // 创建用户代金券
      const now = new Date();
      let expiryTime = null;
      if (coupon.validDays) {
        expiryTime = new Date(
          now.getTime() + coupon.validDays * 24 * 60 * 60 * 1000
        );
      }

      // 设置剩余使用次数
      let remainTimes = 1; // 默认一次使用
      if (coupon.usageLimit !== null && coupon.usageLimit !== undefined) {
        remainTimes = coupon.usageLimit;
      }

      // 创建用户代金券
      await this.customerCouponService.createCoupon({
        customerId: order.customerId,
        couponId: order.couponId,
        receiveTime: now,
        expiryTime,
        remainTimes,
        status: 'unused',
      });

      this.logger.info('【微信支付回调】代金券订单支付成功：', out_trade_no);
    } else {
      this.logger.info('【微信支付回调】代金券订单支付状态异常：', trade_state);
    }
  }

  /**
   * 处理追加服务订单支付回调
   * @param out_trade_no 商户订单号
   * @param transaction_id 微信支付订单号
   * @param trade_state 交易状态
   */
  private async handleAdditionalServiceOrderCallback(
    out_trade_no: string,
    transaction_id: string,
    trade_state: string
  ) {
    // 查询追加服务订单
    const order = await AdditionalServiceOrder.findOne({
      where: {
        sn: out_trade_no,
      },
    });

    if (!order) {
      this.logger.error('【微信支付回调】追加服务订单不存在：', out_trade_no);
      return;
    }

    // 判断交易状态
    if (trade_state === 'SUCCESS') {
      if (order.status === AdditionalServiceOrderStatus.CONFIRMED) {
        // 更新订单状态
        await order.update({
          status: AdditionalServiceOrderStatus.PAID,
          payTime: new Date(),
        });

        // 更新主订单的追加服务信息
        await this.additionalServiceOrderService.updateMainOrderAdditionalServiceInfo(
          order.orderDetailId
        );
      }

      this.logger.info('【微信支付回调】追加服务订单支付成功：', out_trade_no);
    } else {
      this.logger.info('【微信支付回调】追加服务订单支付状态异常：', trade_state);
    }
  }
}
