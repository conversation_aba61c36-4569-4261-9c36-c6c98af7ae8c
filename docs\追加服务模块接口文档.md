# 追加服务模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 追加服务订单状态枚举
```typescript
enum AdditionalServiceOrderStatus {
  PENDING_CONFIRM = 'pending_confirm',    // 待确认
  CONFIRMED = 'confirmed',                // 已确认
  REJECTED = 'rejected',                  // 已拒绝
  PENDING_PAYMENT = 'pending_payment',    // 待付款
  PAID = 'paid',                         // 已付款/服务中
  COMPLETED = 'completed',               // 已完成
  CANCELLED = 'cancelled',               // 已取消
  REFUNDING = 'refunding',               // 退款中
  REFUNDED = 'refunded'                  // 已退款
}
```

---

## 1. 用户端接口

### 1.1 创建追加服务申请
**接口地址：** `POST /order-details/{orderDetailId}/additional-services`  
**接口描述：** 用户为服务中的订单申请追加服务  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |

**请求参数：**
```json
{
  "customerId": 123,
  "services": [
    {
      "serviceId": 1,
      "quantity": 2
    }
  ],
  "discountInfos": [
    {
      "discountType": "coupon",
      "discountId": 1,
      "discountAmount": 10.00
    }
  ],
  "remark": "需要额外清洁"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| services | array | 是 | 追加服务列表 |
| services[].serviceId | number | 是 | 服务ID |
| services[].quantity | number | 是 | 服务数量 |
| discountInfos | array | 否 | 优惠信息列表 |
| discountInfos[].discountType | string | 否 | 优惠类型：coupon-代金券，membership_card-权益卡 |
| discountInfos[].discountId | number | 否 | 优惠ID |
| discountInfos[].discountAmount | number | 否 | 优惠金额 |
| remark | string | 否 | 备注 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "orderDetailId": 123,
    "customerId": 456,
    "status": "pending_confirm",
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 0.00,
    "couponDeduction": 10.00,
    "createdAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### 1.2 查询追加服务列表
**接口地址：** `GET /order-details/{orderDetailId}/additional-services`  
**接口描述：** 查询指定订单详情的追加服务列表  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 否 | 状态筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "sn": "ADD1703123456781234",
      "orderDetailId": 123,
      "status": "pending_confirm",
      "originalPrice": 100.00,
      "totalFee": 90.00,
      "details": [
        {
          "id": 1,
          "serviceId": 1,
          "serviceName": "深度清洁",
          "servicePrice": 50.00,
          "quantity": 2,
          "service": {
            "id": 1,
            "serviceName": "深度清洁"
          }
        }
      ],
      "customer": {
        "id": 456,
        "name": "张三",
        "phone": "13800138000"
      },
      "createdAt": "2023-12-21T10:30:00.000Z"
    }
  ]
}
```

### 1.3 查询追加服务详情
**接口地址：** `GET /order-details/{orderDetailId}/additional-services/{id}`  
**接口描述：** 查询指定追加服务的详细信息  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "orderDetailId": 123,
    "status": "confirmed",
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 0.00,
    "couponDeduction": 10.00,
    "confirmTime": "2023-12-21T11:00:00.000Z",
    "details": [
      {
        "id": 1,
        "serviceId": 1,
        "serviceName": "深度清洁",
        "servicePrice": 50.00,
        "quantity": 2
      }
    ],
    "discountInfos": [
      {
        "id": 1,
        "discountType": "coupon",
        "discountId": 1,
        "discountAmount": 10.00
      }
    ],
    "orderDetail": {
      "id": 123,
      "order": {
        "id": 100,
        "sn": "1703123456781234",
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        }
      }
    }
  }
}
```

### 1.4 支付追加服务订单
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/pay`
**接口描述：** 用户支付已确认的追加服务订单
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "customerId": 456
}
```

**响应示例：**

**0元订单（直接支付成功）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "message": "0元订单支付成功"
  }
}
```

**需要微信支付的订单：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": false,
    "needPay": true,
    "orderSn": "ADD1703123456781234",
    "totalFee": 90.00,
    "message": "需要调用微信支付"
  }
}
```

**说明：**
- 如果是0元订单，直接支付成功，订单状态变为已支付
- 如果需要微信支付，前端需要使用返回的`orderSn`调用微信支付接口
- 微信支付成功后，系统会通过支付回调自动更新订单状态

### 1.5 获取追加服务微信支付参数
**接口地址：** `POST /wepay/jsapi`
**接口描述：** 获取追加服务订单的微信支付参数
**是否需要认证：** 是

**请求参数：**
```json
{
  "appid": "wx1234567890abcdef",
  "sn": "ADD1703123456781234"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "prepay_id": "wx123456789012345678901234567890"
  }
}
```

**说明：**
- 此接口与主订单支付使用相同的接口，系统会根据订单号前缀自动识别订单类型
- 追加服务订单号以"ADD"开头
- 返回的prepay_id用于调用微信支付

### 1.6 同步追加服务支付状态
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/sync-payment-status`
**接口描述：** 通过查询微信支付状态来同步本地订单支付状态
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "operatorId": 123
}
```

**响应示例：**

**同步成功（从微信支付状态更新）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "syncFromWechat": true,
    "message": "根据微信支付状态更新为已支付",
    "localStatus": "paid",
    "wechatStatus": "SUCCESS",
    "wechatTransactionId": "4200001234567890123456789",
    "payTime": "2023-12-21T10:30:00.000Z"
  }
}
```

**订单已经是已支付状态：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "alreadyPaid": true,
    "message": "订单已经是已支付状态",
    "localStatus": "paid",
    "payTime": "2023-12-21T10:30:00.000Z"
  }
}
```

**0元订单直接更新：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "isZeroAmount": true,
    "message": "0元订单已更新为已支付状态",
    "localStatus": "paid",
    "payTime": "2023-12-21T10:30:00.000Z"
  }
}
```

**微信支付未成功：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": false,
    "message": "微信支付状态为 NOTPAY，订单未支付成功",
    "localStatus": "confirmed",
    "wechatStatus": "NOTPAY"
  }
}
```

**说明：**
- 此接口用于解决支付成功但订单状态未更新的异常情况
- 会查询微信支付系统的实际支付状态，并据此更新本地订单状态
- 只有状态为"已确认"的订单才能进行同步
- 0元订单会直接更新为已支付状态

### 1.7 删除追加服务申请
**接口地址：** `DELETE /order-details/{orderDetailId}/additional-services/{id}`
**接口描述：** 用户删除待确认状态的追加服务申请
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "customerId": 456
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**业务规则：**
- 只能删除状态为"待确认"的追加服务申请
- 用户只能删除自己的追加服务申请
- 删除操作会同时删除相关的明细和优惠信息

---

## 2. 员工端接口

### 2.1 查询待确认的追加服务列表
**接口地址：** `GET /employee/additional-services/pending`  
**接口描述：** 员工查询待确认的追加服务申请列表  
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "pending_confirm",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "details": [
          {
            "id": 1,
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2,
            "service": {
              "id": 1,
              "serviceName": "深度清洁"
            }
          }
        ],
        "orderDetail": {
          "id": 123,
          "order": {
            "id": 100,
            "sn": "1703123456781234",
            "customer": {
              "id": 456,
              "name": "张三",
              "phone": "13800138000"
            }
          }
        },
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "createdAt": "2023-12-21T10:30:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 10
  }
}
```

### 2.2 员工确认追加服务
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/confirm`  
**接口描述：** 员工确认追加服务申请  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "employeeId": 789
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 2.3 员工拒绝追加服务
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/reject`  
**接口描述：** 员工拒绝追加服务申请  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "employeeId": 789,
  "rejectReason": "当前时间不适合追加此服务"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| rejectReason | string | 是 | 拒绝原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

---

## 3. 管理端接口

### 3.1 查询订单列表（包含追加服务信息）
**接口地址：** `GET /orders`  
**接口描述：** 查询订单列表，支持查询追加服务信息  
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| phone | string | 否 | 客户手机号筛选 |
| employeename | string | 否 | 员工姓名筛选 |
| status | string | 否 | 订单状态筛选 |
| includeAdditionalServices | string | 否 | 是否包含追加服务详情，传"true"时返回详情 |

**响应示例（不包含追加服务详情）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 100,
        "sn": "1703123456781234",
        "status": "服务中",
        "totalFee": 200.00,
        "hasAdditionalServices": true,
        "additionalServiceAmount": 90.00,
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "orderDetails": [
          {
            "id": 123,
            "serviceName": "基础洗护",
            "servicePrice": 100.00
          }
        ]
      }
    ],
    "total": 1
  }
}
```

**响应示例（包含追加服务详情）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 100,
        "sn": "1703123456781234",
        "status": "服务中",
        "totalFee": 200.00,
        "hasAdditionalServices": true,
        "additionalServiceAmount": 90.00,
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "orderDetails": [
          {
            "id": 123,
            "serviceName": "基础洗护",
            "servicePrice": 100.00,
            "additionalServiceOrders": [
              {
                "id": 1,
                "sn": "ADD1703123456781234",
                "status": "paid",
                "totalFee": 90.00,
                "details": [
                  {
                    "id": 1,
                    "serviceName": "深度清洁",
                    "servicePrice": 50.00,
                    "quantity": 2,
                    "service": {
                      "id": 1,
                      "serviceName": "深度清洁"
                    }
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    "total": 1
  }
}
```

---

## 4. 业务规则说明

### 4.1 状态流转规则
1. **待确认** → **已确认** → **待付款** → **已付款/服务中** → **已完成**
2. **待确认** → **已拒绝**
3. **已确认** → **已取消**
4. **待付款** → **已取消**
5. **已付款/服务中** → **退款中** → **已退款**

### 4.2 业务限制
1. 只有状态为"服务中"的订单才能申请追加服务
2. 员工只能确认/拒绝自己负责的订单的追加服务
3. 用户只能支付已确认的追加服务
4. 追加服务的退款需要通过主订单的退款流程处理

### 4.3 价格计算
1. 原价 = 各服务价格 × 数量的总和
2. 实际支付金额 = 原价 - 权益卡抵扣 - 代金券抵扣
3. 主订单的追加服务总金额会实时更新

### 4.4 通知机制
1. 用户申请追加服务后，员工端收到待确认通知
2. 员工确认后，用户端收到付款通知
3. 员工拒绝后，用户端收到拒绝通知
4. 用户付款成功后，更新主订单的追加服务信息

---

## 3. 管理端接口

### 3.1 查询订单追加服务信息
**接口地址：** `GET /admin/orders/{orderId}/additional-services`
**接口描述：** 管理端查询指定订单的所有追加服务信息
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 主订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "orderId": 100,
    "orderSn": "1703123456781234",
    "additionalServiceSummary": {
      "totalCount": 2,
      "totalAmount": 180.00,
      "totalOriginalPrice": 200.00,
      "totalDeduction": 20.00,
      "paidCount": 1,
      "paidAmount": 90.00,
      "pendingCount": 1,
      "pendingAmount": 90.00
    },
    "additionalServices": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "paid",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "cardDeduction": 0.00,
        "couponDeduction": 10.00,
        "payTime": "2023-12-21T12:00:00.000Z",
        "details": [
          {
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2
          }
        ],
        "createdAt": "2023-12-21T10:30:00.000Z"
      },
      {
        "id": 2,
        "sn": "ADD1703123456781235",
        "status": "confirmed",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "cardDeduction": 10.00,
        "couponDeduction": 0.00,
        "confirmTime": "2023-12-21T11:30:00.000Z",
        "details": [
          {
            "serviceName": "除菌消毒",
            "servicePrice": 30.00,
            "quantity": 3
          }
        ],
        "createdAt": "2023-12-21T11:00:00.000Z"
      }
    ]
  }
}
```

### 3.2 追加服务订单列表（管理端）
**接口地址：** `GET /admin/additional-services`
**接口描述：** 管理端查询追加服务订单列表，支持多维度筛选
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| status | string | 否 | 状态筛选 |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| customerPhone | string | 否 | 客户手机号 |
| employeeId | number | 否 | 员工ID |
| orderSn | string | 否 | 主订单号 |
| additionalServiceSn | string | 否 | 追加服务订单号 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "paid",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "cardDeduction": 0.00,
        "couponDeduction": 10.00,
        "payTime": "2023-12-21T12:00:00.000Z",
        "confirmTime": "2023-12-21T11:00:00.000Z",
        "mainOrder": {
          "id": 100,
          "sn": "1703123456781234",
          "status": "服务中",
          "customer": {
            "id": 456,
            "nickname": "张三",
            "phone": "13800138000"
          },
          "employee": {
            "id": 789,
            "name": "李师傅",
            "phone": "13900139000"
          }
        },
        "details": [
          {
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2
          }
        ],
        "createdAt": "2023-12-21T10:30:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 20,
    "summary": {
      "totalCount": 1,
      "totalAmount": 90.00,
      "totalOriginalPrice": 100.00,
      "totalDeduction": 10.00
    }
  }
}
```

### 3.3 追加服务数据统计
**接口地址：** `GET /admin/additional-services/statistics`
**接口描述：** 管理端查询追加服务数据统计
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| groupBy | string | 否 | 分组方式：day-按天，month-按月，默认不分组 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "overview": {
      "totalOrders": 156,
      "totalAmount": 15600.00,
      "totalOriginalPrice": 18200.00,
      "totalDeduction": 2600.00,
      "averageOrderAmount": 100.00,
      "conversionRate": 0.78,
      "statusDistribution": {
        "pending_confirm": 12,
        "confirmed": 8,
        "paid": 120,
        "rejected": 16
      }
    },
    "trends": [
      {
        "date": "2023-12-21",
        "orderCount": 15,
        "totalAmount": 1500.00,
        "averageAmount": 100.00
      },
      {
        "date": "2023-12-22",
        "orderCount": 18,
        "totalAmount": 1800.00,
        "averageAmount": 100.00
      }
    ],
    "topServices": [
      {
        "serviceId": 1,
        "serviceName": "深度清洁",
        "orderCount": 45,
        "totalAmount": 4500.00,
        "averagePrice": 100.00
      },
      {
        "serviceId": 2,
        "serviceName": "除菌消毒",
        "orderCount": 38,
        "totalAmount": 3800.00,
        "averagePrice": 100.00
      }
    ],
    "employeeRanking": [
      {
        "employeeId": 789,
        "employeeName": "李师傅",
        "orderCount": 25,
        "totalAmount": 2500.00,
        "confirmationRate": 0.85
      }
    ]
  }
}
```

### 3.4 订单列表（包含追加服务信息）
**接口地址：** `GET /admin/orders`
**接口描述：** 管理端订单列表，在原有订单信息基础上增加追加服务摘要信息
**是否需要认证：** 是

**说明：** 此接口已在现有订单控制器中实现，通过 `includeAdditionalServices` 参数控制是否包含追加服务信息

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| includeAdditionalServices | boolean | 否 | 是否包含追加服务信息，默认true |
| ...其他原有订单查询参数 | | | |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 100,
        "sn": "1703123456781234",
        "status": "服务中",
        "totalFee": 200.00,
        "customer": {
          "id": 456,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "employee": {
          "id": 789,
          "name": "李师傅"
        },
        "additionalServiceSummary": {
          "hasAdditionalServices": true,
          "totalCount": 2,
          "totalAmount": 180.00,
          "paidCount": 1,
          "paidAmount": 90.00,
          "pendingCount": 1,
          "pendingAmount": 90.00,
          "lastAdditionalServiceTime": "2023-12-21T11:00:00.000Z"
        },
        "createdAt": "2023-12-21T09:00:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 20
  }
}
```

### 3.5 手动同步追加服务支付状态
**接口地址：** `POST /admin/additional-services/{id}/sync-payment-status`
**接口描述：** 管理端手动同步追加服务支付状态
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "支付异常，手动同步状态"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "syncFromWechat": true,
    "message": "根据微信支付状态更新为已支付",
    "localStatus": "paid",
    "wechatStatus": "SUCCESS",
    "wechatTransactionId": "4200001234567890123456789",
    "payTime": "2023-12-21T10:30:00.000Z",
    "operator": {
      "id": 123,
      "name": "管理员"
    }
  }
}
```

### 3.6 查询微信支付状态
**接口地址：** `GET /admin/wepay/transactions/sn/{sn}`
**接口描述：** 管理端查询微信支付状态，支持自动同步本地订单状态
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sn | string | 是 | 订单号（支持主订单、追加服务、权益卡、代金券） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "out_trade_no": "ADD1703123456781234",
    "transaction_id": "4200001234567890123456789",
    "trade_state": "SUCCESS",
    "trade_state_desc": "支付成功",
    "amount": {
      "total": 9000,
      "currency": "CNY"
    },
    "success_time": "2023-12-21T10:30:00+08:00",
    "localSyncResult": {
      "synced": true,
      "message": "本地订单状态已同步更新"
    }
  }
}
```

**说明：**
- 此接口会自动检查并同步本地订单状态
- 支持所有类型的订单号查询
- 返回微信支付原始数据和本地同步结果

### 3.7 追加服务退款处理
**接口地址：** `POST /admin/additional-services/{id}/refund`
**接口描述：** 管理端处理追加服务退款
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "服务质量问题退款",
  "refundAmount": 90.00,
  "shouldRefundCoupons": true
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operatorId | number | 是 | 操作员ID |
| reason | string | 是 | 退款原因 |
| refundAmount | number | 否 | 退款金额，不填则全额退款 |
| shouldRefundCoupons | boolean | 否 | 是否退回优惠券，默认true |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "refundAmount": 90.00,
    "refundStatus": "processing",
    "message": "退款申请已提交，预计1-3个工作日到账"
  }
}
```

### 3.8 批量操作追加服务订单
**接口地址：** `POST /admin/additional-services/batch-operation`
**接口描述：** 管理端批量操作追加服务订单
**是否需要认证：** 是

**请求参数：**
```json
{
  "operation": "sync_payment_status",
  "orderIds": [1, 2, 3, 4, 5],
  "operatorId": 123,
  "reason": "批量同步支付状态"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operation | string | 是 | 操作类型：sync_payment_status-同步支付状态 |
| orderIds | array | 是 | 追加服务订单ID列表 |
| operatorId | number | 是 | 操作员ID |
| reason | string | 否 | 操作原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCount": 5,
    "successCount": 4,
    "failedCount": 1,
    "results": [
      {
        "orderId": 1,
        "success": true,
        "message": "同步成功"
      },
      {
        "orderId": 2,
        "success": false,
        "message": "订单不存在"
      }
    ]
  }
}
```

---

## 4. 管理端前端集成建议

### 4.1 订单列表页面集成
在标准订单列表中集成追加服务信息：

```javascript
// 订单列表接口调用
const orderList = await api.get('/admin/orders', {
  params: {
    includeAdditionalServices: true,
    current: 1,
    pageSize: 20
  }
});

// 在订单列表中显示追加服务摘要
orderList.data.list.forEach(order => {
  if (order.additionalServiceSummary?.hasAdditionalServices) {
    // 显示追加服务标识
    // 显示追加服务总金额
    // 显示待处理数量等
  }
});
```

### 4.2 订单详情页面集成
在订单详情页面中显示完整的追加服务信息：

```javascript
// 查询订单的追加服务详情
const additionalServices = await api.get(`/admin/orders/${orderId}/additional-services`);

// 显示追加服务列表
// 提供状态同步、退款等操作按钮
```

### 4.3 数据分析页面集成
在数据分析中包含追加服务数据：

```javascript
// 获取追加服务统计数据
const statistics = await api.get('/admin/additional-services/statistics', {
  params: {
    startDate: '2023-12-01',
    endDate: '2023-12-31',
    groupBy: 'day'
  }
});

// 将追加服务数据合并到总体营收分析中
const totalRevenue = mainOrderRevenue + statistics.data.overview.totalAmount;
```

### 4.4 异常处理页面
提供专门的追加服务异常处理页面：

```javascript
// 查询支付异常的追加服务订单
const abnormalOrders = await api.get('/admin/additional-services', {
  params: {
    status: 'confirmed',
    startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD')
  }
});

// 批量同步支付状态
const syncResult = await api.post('/admin/additional-services/batch-operation', {
  operation: 'sync_payment_status',
  orderIds: abnormalOrders.data.list.map(order => order.id),
  operatorId: currentUser.id,
  reason: '定时同步支付状态'
});
```

---

## 5. 数据库字段说明

### 5.1 主订单表新增字段
为了性能优化，建议在主订单相关表中添加冗余字段：

```sql
-- orders 表新增字段
ALTER TABLE orders ADD COLUMN additional_service_amount DECIMAL(8,2) DEFAULT 0.00 COMMENT '追加服务总金额';
ALTER TABLE orders ADD COLUMN additional_service_count INT DEFAULT 0 COMMENT '追加服务订单数量';
ALTER TABLE orders ADD COLUMN has_additional_services BOOLEAN DEFAULT FALSE COMMENT '是否有追加服务';

-- order_details 表新增字段
ALTER TABLE order_details ADD COLUMN additional_service_amount DECIMAL(8,2) DEFAULT 0.00 COMMENT '该服务项的追加服务总金额';
ALTER TABLE order_details ADD COLUMN additional_service_count INT DEFAULT 0 COMMENT '该服务项的追加服务订单数量';
```

### 5.2 字段更新时机
这些冗余字段会在以下时机自动更新：
1. 追加服务订单创建时
2. 追加服务订单状态变更时（确认、支付、退款等）
3. 追加服务订单删除时

### 5.3 查询性能优化
通过冗余字段可以避免复杂的JOIN查询：

```sql
-- 快速查询有追加服务的订单
SELECT * FROM orders WHERE has_additional_services = TRUE;

-- 快速统计追加服务总收入
SELECT SUM(additional_service_amount) FROM orders WHERE status = 'completed';
```

---

## 6. 接口实现状态

### 6.1 已实现的接口

#### 用户端接口（✅ 已完成）
- ✅ `POST /order-details/{orderDetailId}/additional-services` - 创建追加服务申请
- ✅ `GET /order-details/{orderDetailId}/additional-services` - 查询追加服务列表
- ✅ `GET /order-details/{orderDetailId}/additional-services/{id}` - 查询追加服务详情
- ✅ `POST /order-details/{orderDetailId}/additional-services/{id}/pay` - 支付追加服务订单
- ✅ `POST /wepay/jsapi` - 获取微信支付参数（支持追加服务）
- ✅ `POST /order-details/{orderDetailId}/additional-services/{id}/sync-payment-status` - 同步支付状态
- ✅ `DELETE /order-details/{orderDetailId}/additional-services/{id}` - 删除追加服务申请

#### 员工端接口（✅ 已完成）
- ✅ `GET /employee/additional-services/pending` - 查询待确认的追加服务列表
- ✅ `POST /order-details/{orderDetailId}/additional-services/{id}/confirm` - 员工确认追加服务
- ✅ `POST /order-details/{orderDetailId}/additional-services/{id}/reject` - 员工拒绝追加服务

#### 管理端接口（✅ 新增完成）
- ✅ `GET /admin/orders/{orderId}/additional-services` - 查询订单追加服务信息
- ✅ `GET /admin/additional-services` - 追加服务订单列表
- ✅ `GET /admin/additional-services/statistics` - 追加服务数据统计
- ✅ `GET /admin/orders` - 订单列表（包含追加服务信息）
- ✅ `POST /admin/additional-services/{id}/sync-payment-status` - 手动同步支付状态
- ✅ `GET /admin/wepay/transactions/sn/{sn}` - 查询微信支付状态
- ✅ `POST /admin/additional-services/batch-operation` - 批量操作

#### 支付相关接口（✅ 已优化）
- ✅ `GET /wepay/transactions/sn/{sn}` - 查询微信支付状态（支持自动状态同步）
- ✅ 支付回调处理（支持追加服务订单）

### 6.2 核心功能特性

#### 支付流程优化（✅ 已完成）
- ✅ 0元订单自动支付处理
- ✅ 微信支付集成（与主订单一致）
- ✅ 支付回调自动状态更新
- ✅ 支付状态异常自动修复机制

#### 数据一致性（✅ 已完成）
- ✅ 主订单追加服务信息实时更新
- ✅ 冗余字段自动维护
- ✅ 优惠券使用和退回逻辑

#### 管理功能（✅ 已完成）
- ✅ 多维度数据筛选和统计
- ✅ 批量操作支持
- ✅ 异常状态修复工具
- ✅ 详细的操作日志记录

### 6.3 前端集成建议

#### 管理端集成要点
1. **订单列表集成**：在现有订单列表中添加追加服务标识和摘要信息
2. **订单详情集成**：在订单详情页面中显示完整的追加服务信息
3. **数据分析集成**：将追加服务数据纳入整体营收分析
4. **异常处理页面**：提供专门的追加服务异常处理和状态同步功能

#### 关键接口调用示例
```javascript
// 1. 订单列表（包含追加服务摘要）
const orders = await api.get('/admin/orders', {
  params: { includeAdditionalServices: true }
});

// 2. 订单追加服务详情
const additionalServices = await api.get(`/admin/orders/${orderId}/additional-services`);

// 3. 追加服务统计数据
const statistics = await api.get('/admin/additional-services/statistics');

// 4. 异常状态同步
const syncResult = await api.post(`/admin/additional-services/${id}/sync-payment-status`, {
  operatorId: currentUser.id
});
```

### 6.4 部署注意事项

1. **数据库迁移**：确保追加服务相关表结构已正确创建
2. **权限配置**：管理端接口需要适当的权限控制
3. **日志监控**：关注支付回调和状态同步的日志
4. **性能优化**：大数据量时考虑添加适当的数据库索引

所有核心功能已实现完成，可以开始前端开发和集成工作。
