# 追加服务模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 追加服务订单状态枚举
```typescript
enum AdditionalServiceOrderStatus {
  PENDING_CONFIRM = 'pending_confirm',    // 待确认
  CONFIRMED = 'confirmed',                // 已确认
  REJECTED = 'rejected',                  // 已拒绝
  PENDING_PAYMENT = 'pending_payment',    // 待付款
  PAID = 'paid',                         // 已付款/服务中
  COMPLETED = 'completed',               // 已完成
  CANCELLED = 'cancelled',               // 已取消
  REFUNDING = 'refunding',               // 退款中
  REFUNDED = 'refunded'                  // 已退款
}
```

---

## 1. 用户端接口

### 1.1 创建追加服务申请
**接口地址：** `POST /order-details/{orderDetailId}/additional-services`  
**接口描述：** 用户为服务中的订单申请追加服务  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |

**请求参数：**
```json
{
  "customerId": 123,
  "services": [
    {
      "serviceId": 1,
      "quantity": 2
    }
  ],
  "discountInfos": [
    {
      "discountType": "coupon",
      "discountId": 1,
      "discountAmount": 10.00
    }
  ],
  "remark": "需要额外清洁"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| services | array | 是 | 追加服务列表 |
| services[].serviceId | number | 是 | 服务ID |
| services[].quantity | number | 是 | 服务数量 |
| discountInfos | array | 否 | 优惠信息列表 |
| discountInfos[].discountType | string | 否 | 优惠类型：coupon-代金券，membership_card-权益卡 |
| discountInfos[].discountId | number | 否 | 优惠ID |
| discountInfos[].discountAmount | number | 否 | 优惠金额 |
| remark | string | 否 | 备注 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "orderDetailId": 123,
    "customerId": 456,
    "status": "pending_confirm",
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 0.00,
    "couponDeduction": 10.00,
    "createdAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### 1.2 查询追加服务列表
**接口地址：** `GET /order-details/{orderDetailId}/additional-services`  
**接口描述：** 查询指定订单详情的追加服务列表  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 否 | 状态筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "sn": "ADD1703123456781234",
      "orderDetailId": 123,
      "status": "pending_confirm",
      "originalPrice": 100.00,
      "totalFee": 90.00,
      "details": [
        {
          "id": 1,
          "serviceId": 1,
          "serviceName": "深度清洁",
          "servicePrice": 50.00,
          "quantity": 2,
          "service": {
            "id": 1,
            "serviceName": "深度清洁"
          }
        }
      ],
      "customer": {
        "id": 456,
        "name": "张三",
        "phone": "13800138000"
      },
      "createdAt": "2023-12-21T10:30:00.000Z"
    }
  ]
}
```

### 1.3 查询追加服务详情
**接口地址：** `GET /order-details/{orderDetailId}/additional-services/{id}`  
**接口描述：** 查询指定追加服务的详细信息  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "orderDetailId": 123,
    "status": "confirmed",
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 0.00,
    "couponDeduction": 10.00,
    "confirmTime": "2023-12-21T11:00:00.000Z",
    "details": [
      {
        "id": 1,
        "serviceId": 1,
        "serviceName": "深度清洁",
        "servicePrice": 50.00,
        "quantity": 2
      }
    ],
    "discountInfos": [
      {
        "id": 1,
        "discountType": "coupon",
        "discountId": 1,
        "discountAmount": 10.00
      }
    ],
    "orderDetail": {
      "id": 123,
      "order": {
        "id": 100,
        "sn": "1703123456781234",
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        }
      }
    }
  }
}
```

### 1.4 支付追加服务订单
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/pay`
**接口描述：** 用户支付已确认的追加服务订单
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "customerId": 456
}
```

**响应示例：**

**0元订单（直接支付成功）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "message": "0元订单支付成功"
  }
}
```

**需要微信支付的订单：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": false,
    "needPay": true,
    "orderSn": "ADD1703123456781234",
    "totalFee": 90.00,
    "message": "需要调用微信支付"
  }
}
```

**说明：**
- 如果是0元订单，直接支付成功，订单状态变为已支付
- 如果需要微信支付，前端需要使用返回的`orderSn`调用微信支付接口
- 微信支付成功后，系统会通过支付回调自动更新订单状态

### 1.5 获取追加服务微信支付参数
**接口地址：** `POST /wepay/jsapi`
**接口描述：** 获取追加服务订单的微信支付参数
**是否需要认证：** 是

**请求参数：**
```json
{
  "appid": "wx1234567890abcdef",
  "sn": "ADD1703123456781234"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "prepay_id": "wx123456789012345678901234567890"
  }
}
```

**说明：**
- 此接口与主订单支付使用相同的接口，系统会根据订单号前缀自动识别订单类型
- 追加服务订单号以"ADD"开头
- 返回的prepay_id用于调用微信支付

### 1.6 删除追加服务申请
**接口地址：** `DELETE /order-details/{orderDetailId}/additional-services/{id}`
**接口描述：** 用户删除待确认状态的追加服务申请
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "customerId": 456
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**业务规则：**
- 只能删除状态为"待确认"的追加服务申请
- 用户只能删除自己的追加服务申请
- 删除操作会同时删除相关的明细和优惠信息

---

## 2. 员工端接口

### 2.1 查询待确认的追加服务列表
**接口地址：** `GET /employee/additional-services/pending`  
**接口描述：** 员工查询待确认的追加服务申请列表  
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "pending_confirm",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "details": [
          {
            "id": 1,
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2,
            "service": {
              "id": 1,
              "serviceName": "深度清洁"
            }
          }
        ],
        "orderDetail": {
          "id": 123,
          "order": {
            "id": 100,
            "sn": "1703123456781234",
            "customer": {
              "id": 456,
              "name": "张三",
              "phone": "13800138000"
            }
          }
        },
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "createdAt": "2023-12-21T10:30:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 10
  }
}
```

### 2.2 员工确认追加服务
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/confirm`  
**接口描述：** 员工确认追加服务申请  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "employeeId": 789
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 2.3 员工拒绝追加服务
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/reject`  
**接口描述：** 员工拒绝追加服务申请  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "employeeId": 789,
  "rejectReason": "当前时间不适合追加此服务"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| rejectReason | string | 是 | 拒绝原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

---

## 3. 管理端接口

### 3.1 查询订单列表（包含追加服务信息）
**接口地址：** `GET /orders`  
**接口描述：** 查询订单列表，支持查询追加服务信息  
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| phone | string | 否 | 客户手机号筛选 |
| employeename | string | 否 | 员工姓名筛选 |
| status | string | 否 | 订单状态筛选 |
| includeAdditionalServices | string | 否 | 是否包含追加服务详情，传"true"时返回详情 |

**响应示例（不包含追加服务详情）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 100,
        "sn": "1703123456781234",
        "status": "服务中",
        "totalFee": 200.00,
        "hasAdditionalServices": true,
        "additionalServiceAmount": 90.00,
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "orderDetails": [
          {
            "id": 123,
            "serviceName": "基础洗护",
            "servicePrice": 100.00
          }
        ]
      }
    ],
    "total": 1
  }
}
```

**响应示例（包含追加服务详情）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 100,
        "sn": "1703123456781234",
        "status": "服务中",
        "totalFee": 200.00,
        "hasAdditionalServices": true,
        "additionalServiceAmount": 90.00,
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "orderDetails": [
          {
            "id": 123,
            "serviceName": "基础洗护",
            "servicePrice": 100.00,
            "additionalServiceOrders": [
              {
                "id": 1,
                "sn": "ADD1703123456781234",
                "status": "paid",
                "totalFee": 90.00,
                "details": [
                  {
                    "id": 1,
                    "serviceName": "深度清洁",
                    "servicePrice": 50.00,
                    "quantity": 2,
                    "service": {
                      "id": 1,
                      "serviceName": "深度清洁"
                    }
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    "total": 1
  }
}
```

---

## 4. 业务规则说明

### 4.1 状态流转规则
1. **待确认** → **已确认** → **待付款** → **已付款/服务中** → **已完成**
2. **待确认** → **已拒绝**
3. **已确认** → **已取消**
4. **待付款** → **已取消**
5. **已付款/服务中** → **退款中** → **已退款**

### 4.2 业务限制
1. 只有状态为"服务中"的订单才能申请追加服务
2. 员工只能确认/拒绝自己负责的订单的追加服务
3. 用户只能支付已确认的追加服务
4. 追加服务的退款需要通过主订单的退款流程处理

### 4.3 价格计算
1. 原价 = 各服务价格 × 数量的总和
2. 实际支付金额 = 原价 - 权益卡抵扣 - 代金券抵扣
3. 主订单的追加服务总金额会实时更新

### 4.4 通知机制
1. 用户申请追加服务后，员工端收到待确认通知
2. 员工确认后，用户端收到付款通知
3. 员工拒绝后，用户端收到拒绝通知
4. 用户付款成功后，更新主订单的追加服务信息
